import PropTypes from 'prop-types';
import VideoEmbed from '../media/VideoEmbed';

/**
 * Component for displaying game details like description and main video
 */
const GameDetails = ({ game }) => {
  return (
    <div>
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-white mb-3">About</h3>
        <p className="text-gray-300 leading-relaxed">{game.description}</p>
      </div>
      {game.main_video_url && (
        <div className="mb-6">
          <VideoEmbed url={game.main_video_url} isMain={true} />
        </div>
      )}
    </div>
  );
};

GameDetails.propTypes = {
  game: PropTypes.shape({
    description: PropTypes.string,
    mainVideo: PropTypes.string
  }).isRequired
};

export default GameDetails; 