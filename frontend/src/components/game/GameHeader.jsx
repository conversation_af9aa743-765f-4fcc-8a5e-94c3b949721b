import PropTypes from 'prop-types';
import StarRating from '../common/StarRating';
import ShareGame from './ShareGame';

/**
 * Component for displaying game header information: title, publisher, rating, etc.
 */
const GameHeader = ({ game, avgRating, reviewCount }) => {
  // Determine if we have platform links to show
  const hasPlatformLinks = game && (game.steamUrl || game.itchUrl || game.epicGamesUrl);
  
  return (
    <div>      
      <h1 className="text-4xl font-bold text-white mb-4">{game.title}</h1>

      {/* Share Game Component */}
      <div className="mb-6">
        <ShareGame gameTitle={game.title} />
      </div>
      
      {/* Game metadata */}
      <div className="space-y-4 mb-6">
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-gray-400">Published by:</span> 
          <span className="text-orange-400 font-medium">{game.publisherName}</span>
        </div>
        
        <div className="flex flex-wrap items-center gap-4">
          <span className="text-gray-400">User Rating:</span>
          <div className="flex items-center gap-2">
            <StarRating rating={avgRating} />
            {reviewCount > 0 ? (
              <span className="text-gray-300 text-sm">
                {avgRating} out of 5 ({reviewCount} {reviewCount === 1 ? 'review' : 'reviews'})
              </span>
            ) : (
              <span className="text-gray-500 text-sm">No ratings yet</span>
            )}
          </div>
        </div>
        
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-gray-400">Release Date:</span> 
          <span className="text-gray-300">{new Date(game.releaseDate).toLocaleDateString()}</span>
        </div>
        
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-gray-400">Price:</span> 
          <span className="text-green-400 font-semibold">{
            game.priceModel === 'free' ? 'Free' :
            game.priceModel === 'paid' ? `$${game.price}` :
            game.priceModel === 'credits' ? `${game.creditPrice} Credits` : 'Free'
          }</span>
        </div>
        
        {hasPlatformLinks && (
          <div className="flex flex-wrap items-center gap-2">
            <span className="text-gray-400">Available on:</span>
            <div className="flex gap-2">
              {game.steamUrl && (
                <a href={game.steamUrl} target="_blank" rel="noopener noreferrer" className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors duration-200">
                  Steam
                </a>
              )}
              {game.itchUrl && (
                <a href={game.itchUrl} target="_blank" rel="noopener noreferrer" className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors duration-200">
                  itch.io
                </a>
              )}
              {game.epicGamesUrl && (
                <a href={game.epicGamesUrl} target="_blank" rel="noopener noreferrer" className="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm transition-colors duration-200">
                  Epic Games
                </a>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

GameHeader.propTypes = {
  game: PropTypes.shape({
    title: PropTypes.string.isRequired,
    publisherName: PropTypes.string,
    releaseDate: PropTypes.string,
    priceModel: PropTypes.string,
    price: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    creditPrice: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    steamUrl: PropTypes.string,
    itchUrl: PropTypes.string,
    epicGamesUrl: PropTypes.string
  }).isRequired,
  avgRating: PropTypes.number.isRequired,
  reviewCount: PropTypes.number.isRequired
};

export default GameHeader; 